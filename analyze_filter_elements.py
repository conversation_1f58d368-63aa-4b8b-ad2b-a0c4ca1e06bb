#!/usr/bin/env python3
"""
分析筛选元素的页面结构
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup


def analyze_filter_elements():
    """分析筛选元素"""
    print("开始分析筛选元素...")
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # 访问目标页面
        url = "https://nqt.nanjing.gov.cn/nqtmh/njyst/pages/policy_market.html"
        print(f"正在访问: {url}")
        driver.get(url)
        
        # 等待页面加载
        time.sleep(5)
        wait = WebDriverWait(driver, 20)
        wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        
        # 先点击市级筛选
        try:
            city_level = driver.find_element(By.XPATH, "//li[@itemvalue='city']//a")
            if city_level:
                print("找到市级选项，正在点击...")
                city_level.click()
                time.sleep(3)
                print("已选择发布层级：市")
        except Exception as e:
            print(f"点击市级选项失败: {e}")
        
        # 获取页面源码
        page_source = driver.page_source
        soup = BeautifulSoup(page_source, 'html.parser')
        
        print("\n=== 分析筛选区域 ===")
        
        # 1. 查找发布日期相关元素
        print("\n1. 发布日期筛选元素:")
        date_elements = soup.find_all(text=lambda x: x and '发布日期' in x)
        for i, elem in enumerate(date_elements):
            parent = elem.parent if elem.parent else None
            if parent:
                print(f"  发布日期元素 {i+1}: {parent.name} - {parent.get('class', 'no-class')}")
                print(f"    HTML: {str(parent)[:200]}...")
        
        # 查找日期相关的按钮和输入框
        date_buttons = soup.find_all('button', text=lambda x: x and '日期' in x)
        date_inputs = soup.find_all('input', type='date')
        date_spans = soup.find_all('span', text=lambda x: x and ('日期' in x or '自定义' in x))
        
        print(f"  日期按钮: {len(date_buttons)} 个")
        print(f"  日期输入框: {len(date_inputs)} 个")
        print(f"  日期相关span: {len(date_spans)} 个")
        
        # 2. 查找发布机构相关元素
        print("\n2. 发布机构筛选元素:")
        org_elements = soup.find_all(text=lambda x: x and '发布机构' in x)
        for i, elem in enumerate(org_elements):
            parent = elem.parent if elem.parent else None
            if parent:
                print(f"  发布机构元素 {i+1}: {parent.name} - {parent.get('class', 'no-class')}")
                print(f"    HTML: {str(parent)[:200]}...")
        
        # 查找可能的展开按钮
        expand_buttons = soup.find_all('i', class_=lambda x: x and ('expand' in str(x) or 'arrow' in str(x) or 'down' in str(x)))
        print(f"  可能的展开按钮: {len(expand_buttons)} 个")
        for i, btn in enumerate(expand_buttons[:3]):
            print(f"    展开按钮 {i+1}: {btn.get('class', 'no-class')}")
        
        # 3. 查找目标机构名称
        print("\n3. 目标机构名称:")
        target_orgs = ["市发改委", "市科技局", "市工信局", "发改委", "科技局", "工信局"]
        for org in target_orgs:
            org_found = soup.find_all(text=lambda x: x and org in x)
            print(f"  {org}: 找到 {len(org_found)} 个匹配")
            for i, found in enumerate(org_found[:2]):
                parent = found.parent if found.parent else None
                if parent:
                    print(f"    匹配 {i+1}: {parent.name} - {parent.get('class', 'no-class')}")
        
        # 4. 查找所有可点击的筛选元素
        print("\n4. 可点击的筛选元素:")
        clickable_elements = soup.find_all(['a', 'button', 'span'], 
                                         class_=lambda x: x and any(keyword in str(x).lower() 
                                                                   for keyword in ['filter', 'select', 'option', 'item']))
        print(f"  可点击筛选元素: {len(clickable_elements)} 个")
        for i, elem in enumerate(clickable_elements[:5]):
            text = elem.get_text(strip=True)[:30]
            print(f"    元素 {i+1}: {elem.name} - {elem.get('class', 'no-class')} - '{text}'")
        
        # 5. 查找筛选区域的整体结构
        print("\n5. 筛选区域结构:")
        filter_containers = soup.find_all('div', class_=lambda x: x and any(keyword in str(x).lower() 
                                                                           for keyword in ['filter', 'search', 'condition']))
        print(f"  筛选容器: {len(filter_containers)} 个")
        for i, container in enumerate(filter_containers[:3]):
            print(f"    容器 {i+1}: {container.get('class', 'no-class')}")
            # 查找容器内的子元素
            children = container.find_all(['span', 'a', 'button', 'input'])
            print(f"      子元素: {len(children)} 个")
        
        # 保存完整的页面源码用于分析
        with open("data/filter_analysis_page.html", "w", encoding="utf-8") as f:
            f.write(page_source)
        
        print(f"\n页面源码已保存到: data/filter_analysis_page.html")
        
        # 6. 尝试使用JavaScript获取更多信息
        print("\n6. JavaScript分析:")
        try:
            # 获取所有包含"发布"文本的元素
            js_script = """
            var elements = [];
            var walker = document.createTreeWalker(
                document.body,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );
            var node;
            while (node = walker.nextNode()) {
                if (node.textContent.includes('发布') || node.textContent.includes('日期') || node.textContent.includes('机构')) {
                    elements.push({
                        text: node.textContent.trim(),
                        parent: node.parentElement.tagName + '.' + (node.parentElement.className || 'no-class'),
                        html: node.parentElement.outerHTML.substring(0, 200)
                    });
                }
            }
            return elements;
            """
            js_results = driver.execute_script(js_script)
            print(f"  JavaScript找到 {len(js_results)} 个相关元素")
            for i, result in enumerate(js_results[:5]):
                print(f"    JS元素 {i+1}: {result['parent']} - '{result['text'][:30]}'")
        except Exception as e:
            print(f"  JavaScript分析失败: {e}")
        
        print("\n筛选元素分析完成！")
        
    except Exception as e:
        print(f"分析过程出错: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        driver.quit()


if __name__ == "__main__":
    print("=" * 60)
    print("筛选元素分析工具")
    print("=" * 60)
    
    # 创建数据目录
    os.makedirs("data", exist_ok=True)
    
    analyze_filter_elements()
