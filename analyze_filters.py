#!/usr/bin/env python3
"""
分析页面筛选选项
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup


def analyze_filters():
    """分析筛选选项"""
    print("开始分析页面筛选选项...")
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        # 访问目标页面
        url = "https://nqt.nanjing.gov.cn/nqtmh/njyst/pages/policy_market.html"
        print(f"正在访问: {url}")
        driver.get(url)
        
        # 等待页面加载
        time.sleep(5)
        wait = WebDriverWait(driver, 20)
        wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        
        # 获取页面源码
        page_source = driver.page_source
        soup = BeautifulSoup(page_source, 'html.parser')
        
        print("\n=== 筛选选项分析 ===")
        
        # 1. 查找发布层级筛选
        print("\n1. 发布层级筛选:")
        level_container = soup.find('ul', id='fbcj')
        if level_container:
            level_items = level_container.find_all('li')
            for item in level_items:
                item_value = item.get('itemvalue', '')
                text = item.get_text().strip()
                print(f"   - {text} (value: {item_value})")
        else:
            print("   未找到发布层级筛选容器")
        
        # 2. 查找发布机构筛选
        print("\n2. 发布机构筛选:")
        # 查找所有可能的机构筛选容器
        org_containers = soup.find_all('ul', class_='item-list')
        for i, container in enumerate(org_containers):
            print(f"   容器 {i+1}:")
            items = container.find_all('li')
            for item in items[:10]:  # 只显示前10个
                text = item.get_text().strip()
                if text:
                    print(f"     - {text}")
        
        # 3. 查找政策分类筛选
        print("\n3. 政策分类筛选:")
        policy_type_elements = soup.find_all('li', class_='type-list')
        for element in policy_type_elements:
            label = element.find('span', class_='type-label')
            if label and '政策分类' in label.get_text():
                items = element.find_all('li')
                for item in items[:10]:
                    text = item.get_text().strip()
                    if text and text != '政策分类':
                        print(f"   - {text}")
        
        # 4. 查找适用区域筛选
        print("\n4. 适用区域筛选:")
        area_elements = soup.find_all('li', class_='type-list')
        for element in area_elements:
            label = element.find('span', class_='type-label')
            if label and '适用区域' in label.get_text():
                items = element.find_all('li')
                for item in items[:10]:
                    text = item.get_text().strip()
                    if text and text != '适用区域':
                        print(f"   - {text}")
        
        # 5. 查找行业分类筛选
        print("\n5. 行业分类筛选:")
        industry_elements = soup.find_all('li', class_='type-list')
        for element in industry_elements:
            label = element.find('span', class_='type-label')
            if label and '行业分类' in label.get_text():
                items = element.find_all('li')
                for item in items[:10]:
                    text = item.get_text().strip()
                    if text and text != '行业分类':
                        print(f"   - {text}")
        
        # 6. 测试点击市级筛选
        print("\n6. 测试点击市级筛选:")
        try:
            # 查找市级选项
            city_option = driver.find_element(By.XPATH, "//li[@itemvalue='city']//a")
            if city_option:
                print("   找到市级选项，尝试点击...")
                city_option.click()
                time.sleep(3)
                
                # 重新获取政策列表
                wait.until(EC.presence_of_element_located((By.ID, "policylist")))
                new_page_source = driver.page_source
                new_soup = BeautifulSoup(new_page_source, 'html.parser')
                
                policy_items = new_soup.select("ul#policylist li.market-list")
                print(f"   点击后找到 {len(policy_items)} 个政策")
                
                # 显示前几个政策的发布机构
                for i, item in enumerate(policy_items[:3], 1):
                    title_element = item.select_one("p.market-text")
                    org_element = item.select_one("span.contruct-name")
                    
                    if title_element and org_element:
                        title = title_element.get_text().strip()[:50]
                        org = org_element.get_text().strip()
                        print(f"   {i}. {title}... - {org}")
            else:
                print("   未找到市级选项")
        except Exception as e:
            print(f"   点击市级选项失败: {e}")
        
        # 保存筛选后的页面源码
        with open("data/filtered_page_source.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        
        print(f"\n筛选分析完成！")
        print(f"筛选后的页面源码已保存到: data/filtered_page_source.html")
        
    except Exception as e:
        print(f"分析过程出错: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        driver.quit()


if __name__ == "__main__":
    print("=" * 60)
    print("页面筛选选项分析工具")
    print("=" * 60)
    
    # 创建数据目录
    os.makedirs("data", exist_ok=True)
    
    analyze_filters()
