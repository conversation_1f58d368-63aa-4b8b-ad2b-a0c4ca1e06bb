#!/usr/bin/env python3
"""
测试爬虫功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from crawler import NanjingPolicyCrawler


def test_basic_functionality():
    """测试基本功能"""
    print("开始测试南京市政策爬虫...")
    
    crawler = NanjingPolicyCrawler()
    
    try:
        # 设置WebDriver
        crawler.setup_driver()
        print("✓ WebDriver初始化成功")
        
        # 访问目标页面
        if crawler.navigate_to_target_page():
            print("✓ 成功访问目标页面")
        else:
            print("✗ 访问目标页面失败")
            return False
        
        # 设置筛选条件
        if crawler.set_filters():
            print("✓ 筛选条件设置成功")
        else:
            print("✗ 筛选条件设置失败")
        
        # 获取政策列表（只获取前几条进行测试）
        policies = crawler.get_policy_list()
        print(f"✓ 获取到 {len(policies)} 条政策")
        
        if policies:
            # 测试提取第一条政策的详情
            first_policy = policies[0]
            print(f"\n测试提取政策详情:")
            print(f"标题: {first_policy['title']}")
            print(f"链接: {first_policy['link']}")
            
            # 提取详情
            policy_detail = crawler.extract_policy_detail(first_policy['link'])
            
            if policy_detail:
                print("✓ 成功提取政策详情")
                print(f"详情标题: {policy_detail['title'][:50]}...")
                print(f"发布机构: {policy_detail['fbjg']}")
                print(f"内容长度: {len(policy_detail['content'])} 字符")
                print(f"附件数量: {len(policy_detail['link'])} 个")
                
                # 保存测试数据
                test_data = [policy_detail]
                crawler.save_data(test_data)
                print("✓ 测试数据已保存")
                
            else:
                print("✗ 提取政策详情失败")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试过程出错: {e}")
        return False
        
    finally:
        crawler.close_driver()
        print("✓ WebDriver已关闭")


def test_page_structure():
    """测试页面结构分析"""
    print("\n开始测试页面结构分析...")
    
    crawler = NanjingPolicyCrawler()
    
    try:
        crawler.setup_driver()
        
        if crawler.navigate_to_target_page():
            # 等待页面加载和JavaScript执行
            import time
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.common.by import By

            print("等待页面动态内容加载...")
            time.sleep(10)  # 等待更长时间

            # 尝试等待政策列表加载
            try:
                wait = WebDriverWait(crawler.driver, 20)
                wait.until(EC.presence_of_element_located((By.ID, "policylist")))
                print("政策列表容器已加载")
            except:
                print("政策列表容器加载超时")

            # 获取页面源码进行分析
            page_source = crawler.driver.page_source

            # 保存当前页面源码用于调试
            with open("data/current_page_source.html", "w", encoding="utf-8") as f:
                f.write(page_source)
            
            # 检查关键元素是否存在
            checks = [
                ('政策列表容器', 'id="policylist"' in page_source),
                ('政策列表项', 'market-list' in page_source),
                ('政策标题', 'market-text' in page_source),
                ('发布机构', 'contruct-name' in page_source),
                ('发布层级筛选', 'id="fbcj"' in page_source),
                ('政策链接', 'policy_articalsqt.html?policyguid=' in page_source),
            ]
            
            print("\n页面结构检查:")
            for name, exists in checks:
                status = "✓" if exists else "✗"
                print(f"{status} {name}: {'存在' if exists else '不存在'}")
            
            return all(exists for _, exists in checks)
        
    except Exception as e:
        print(f"✗ 页面结构测试出错: {e}")
        return False
        
    finally:
        crawler.close_driver()


def main():
    """主函数"""
    print("=" * 60)
    print("南京市政策爬虫测试工具")
    print("=" * 60)
    
    # 测试页面结构
    if test_page_structure():
        print("\n页面结构测试通过")
    else:
        print("\n页面结构测试失败，请检查页面是否发生变化")
        return
    
    # 测试基本功能
    if test_basic_functionality():
        print("\n✓ 所有测试通过！爬虫功能正常")
    else:
        print("\n✗ 测试失败，请检查错误信息")


if __name__ == "__main__":
    main()
