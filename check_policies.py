#!/usr/bin/env python3
"""
检查当前页面上的所有政策
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from crawler import NanjingPolicyCrawler
from bs4 import BeautifulSoup
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By


def check_all_policies():
    """检查所有政策"""
    print("开始检查当前页面上的所有政策...")
    
    crawler = NanjingPolicyCrawler()
    
    try:
        # 设置WebDriver
        crawler.setup_driver()
        print("✓ WebDriver初始化成功")
        
        # 访问目标页面
        if crawler.navigate_to_target_page():
            print("✓ 成功访问目标页面")
        else:
            print("✗ 访问目标页面失败")
            return
        
        # 等待页面加载
        time.sleep(5)
        wait = WebDriverWait(crawler.driver, 20)
        wait.until(EC.presence_of_element_located((By.ID, "policylist")))
        
        # 获取页面源码
        page_source = crawler.driver.page_source
        soup = BeautifulSoup(page_source, 'html.parser')
        
        # 查找政策列表
        policy_items = soup.select("ul#policylist li.market-list")
        print(f"✓ 找到 {len(policy_items)} 个政策列表项")
        
        print("\n=== 政策列表详情 ===")
        
        for i, item in enumerate(policy_items, 1):
            try:
                # 提取政策标题
                title_element = item.select_one("p.market-text")
                if not title_element:
                    continue
                
                title = title_element.get_text().strip()
                
                # 从onclick事件中提取链接
                onclick = title_element.get('onclick', '')
                link = ""
                if 'policy_articalsqt.html?policyguid=' in onclick:
                    import re
                    guid_match = re.search(r'policyguid=([A-F0-9-]+)', onclick)
                    if guid_match:
                        guid = guid_match.group(1)
                        link = f"https://nqt.nanjing.gov.cn/nqtmh/njyst/pages/policy_articalsqt.html?policyguid={guid}"
                
                # 提取发布机构
                org_element = item.select_one("span.contruct-name")
                publish_org = org_element.get_text().strip() if org_element else "未知"
                
                # 提取发布日期
                date_text = item.get_text()
                import re
                date_match = re.search(r'发布日期：(\d{4}-\d{2}-\d{2})', date_text)
                publish_date = date_match.group(1) if date_match else "未知"
                
                # 提取发布层级
                level_text = item.get_text()
                level_match = re.search(r'发布层级：([^|]+)', level_text)
                publish_level = level_match.group(1).strip() if level_match else "未知"
                
                print(f"\n{i}. 政策信息:")
                print(f"   标题: {title[:80]}...")
                print(f"   发布机构: {publish_org}")
                print(f"   发布层级: {publish_level}")
                print(f"   发布日期: {publish_date}")
                print(f"   链接: {link}")
                
                # 检查是否符合筛选条件
                is_city_level = "市" in publish_level
                is_target_org = any(org in publish_org for org in ["发改委", "科技局", "工信局"])
                is_recent = True  # 暂时不检查日期
                
                符合条件 = is_city_level and is_target_org and is_recent
                print(f"   符合条件: {'是' if 符合条件 else '否'} (市级: {is_city_level}, 目标机构: {is_target_org})")
                
            except Exception as e:
                print(f"解析第 {i} 条政策失败: {e}")
                continue
        
        print(f"\n检查完成！")
        
    except Exception as e:
        print(f"✗ 检查过程出错: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        crawler.close_driver()
        print("✓ WebDriver已关闭")


if __name__ == "__main__":
    check_all_policies()
