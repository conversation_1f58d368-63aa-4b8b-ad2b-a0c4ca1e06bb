"""
南京市政策爬虫主类
"""
import time
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from bs4 import BeautifulSoup

from .config import *
from .utils import *


class NanjingPolicyCrawler:
    """南京市政策爬虫类"""
    
    def __init__(self):
        """初始化爬虫"""
        self.logger = setup_logging(OUTPUT_CONFIG["log_dir"])
        self.driver = None
        self.wait = None
        self.policies_data = []
        
    def setup_driver(self) -> None:
        """设置Chrome WebDriver"""
        try:
            chrome_options = Options()
            
            # 添加Chrome选项
            for option in CHROME_OPTIONS:
                chrome_options.add_argument(option)
            
            # 创建WebDriver实例
            self.driver = webdriver.Chrome(options=chrome_options)
            
            # 设置超时时间
            self.driver.implicitly_wait(SELENIUM_CONFIG["implicit_wait"])
            self.driver.set_page_load_timeout(SELENIUM_CONFIG["page_load_timeout"])
            self.driver.set_script_timeout(SELENIUM_CONFIG["script_timeout"])
            
            # 设置窗口大小
            self.driver.set_window_size(*SELENIUM_CONFIG["window_size"])
            
            # 创建WebDriverWait实例
            self.wait = WebDriverWait(self.driver, SELENIUM_CONFIG["implicit_wait"])
            
            self.logger.info("WebDriver初始化成功")
            
        except Exception as e:
            self.logger.error(f"WebDriver初始化失败: {e}")
            raise
    
    def close_driver(self) -> None:
        """关闭WebDriver"""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("WebDriver已关闭")
            except Exception as e:
                self.logger.error(f"关闭WebDriver时出错: {e}")
    
    def navigate_to_target_page(self) -> bool:
        """导航到目标页面"""
        try:
            self.logger.info(f"正在访问目标页面: {TARGET_URL}")
            self.driver.get(TARGET_URL)
            
            # 等待页面加载完成
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            self.logger.info("成功访问目标页面")
            return True
            
        except TimeoutException:
            self.logger.error("页面加载超时")
            return False
        except Exception as e:
            self.logger.error(f"访问页面失败: {e}")
            return False
    
    def set_filters(self) -> bool:
        """设置筛选条件"""
        try:
            self.logger.info("开始设置筛选条件")
            
            # 这里需要根据实际页面结构来实现筛选逻辑
            # 由于页面结构复杂，需要分析具体的筛选元素
            
            # 设置发布时间筛选
            self._set_date_filter()
            
            # 设置发布机构筛选
            self._set_org_filter()
            
            self.logger.info("筛选条件设置完成")
            return True
            
        except Exception as e:
            self.logger.error(f"设置筛选条件失败: {e}")
            return False
    
    def _set_date_filter(self) -> None:
        """设置日期筛选"""
        try:
            self.logger.info(f"设置日期筛选: {FILTER_CONFIG['start_date']} 到 {FILTER_CONFIG['end_date']}")

            # 根据页面分析，这个网站可能没有明显的日期筛选器
            # 我们将在获取数据后进行日期过滤
            self.logger.info("将在数据获取后进行日期过滤")

        except Exception as e:
            self.logger.warning(f"设置日期筛选失败: {e}")

    def _set_org_filter(self) -> None:
        """设置发布机构筛选 - 按照人工筛选逻辑"""
        try:
            self.logger.info("开始设置筛选条件...")

            # 第一步：点击发布层级第一行的"市"
            try:
                city_level = self.driver.find_element(By.XPATH, "//li[@itemvalue='city']//a")
                if city_level:
                    self.logger.info("找到发布层级'市'选项，正在点击...")
                    city_level.click()
                    time.sleep(3)  # 等待页面刷新
                    self.logger.info("已选择发布层级：市")

                    # 等待政策列表更新
                    self.wait.until(EC.presence_of_element_located((By.ID, "policylist")))
                    time.sleep(2)
                else:
                    self.logger.warning("未找到市级选项")
                    return
            except Exception as e:
                self.logger.warning(f"设置发布层级失败: {e}")
                return

            # 第二步：设置日期筛选 - 前三年1月1日到现在
            self._set_date_filter_custom()

        except Exception as e:
            self.logger.warning(f"设置筛选条件失败: {e}")

    def _set_date_filter_custom(self) -> None:
        """设置自定义日期筛选"""
        try:
            self.logger.info("设置自定义日期筛选...")

            # 点击日期选择器
            date_selector = self.driver.find_element(By.CSS_SELECTOR, ".uc-timeselect-value")
            if date_selector:
                date_selector.click()
                time.sleep(2)
                self.logger.info("已点击日期选择器")

                # 点击自定义选项
                custom_option = self.driver.find_element(By.CSS_SELECTOR, ".uc-timeselect-range .title")
                if custom_option:
                    custom_option.click()
                    time.sleep(2)
                    self.logger.info("已选择自定义日期")

                    # 设置开始日期（前三年1月1日）
                    from datetime import datetime
                    start_date = datetime.now().replace(year=datetime.now().year-3, month=1, day=1)
                    start_date_str = start_date.strftime("%Y-%m-%d")

                    # 设置结束日期（今天）
                    end_date_str = datetime.now().strftime("%Y-%m-%d")

                    # 点击日期输入框来触发日期选择器
                    date_input = self.driver.find_element(By.ID, "timer_release-time")
                    if date_input:
                        date_input.click()
                        time.sleep(2)

                        # 使用JavaScript设置日期值
                        js_script = f"""
                        var input = document.getElementById('timer_release-time');
                        input.value = '{start_date_str} - {end_date_str}';
                        input.dispatchEvent(new Event('change'));
                        """
                        self.driver.execute_script(js_script)
                        time.sleep(2)

                        # 点击页面其他地方关闭日期选择器
                        self.driver.find_element(By.TAG_NAME, "body").click()
                        time.sleep(2)

                        self.logger.info(f"已设置日期范围: {start_date_str} 到 {end_date_str}")

        except Exception as e:
            self.logger.warning(f"设置日期筛选失败: {e}")

    def set_specific_org_filter(self, org_name: str) -> bool:
        """设置特定机构筛选"""
        try:
            self.logger.info(f"设置特定机构筛选: {org_name}")

            # 机构ID映射
            org_id_map = {
                "市发改委": "e3e92098-dfa4-4757-a8b6-403299497a9d",
                "市科技局": "47b6aa41-1c62-4412-808f-975678aaca89",
                "市工信局": "b10f5c47-bc44-47fa-bd9d-ced04cef7022"
            }

            org_id = org_id_map.get(org_name)
            if not org_id:
                self.logger.warning(f"未找到机构ID: {org_name}")
                return False

            # 首先点击展开按钮
            try:
                expand_button = self.driver.find_element(By.CSS_SELECTOR, ".slide a")
                if expand_button and expand_button.text == "展开":
                    expand_button.click()
                    time.sleep(2)
                    self.logger.info("已展开发布机构选项")
            except Exception as e:
                self.logger.debug(f"展开按钮点击失败或已展开: {e}")

            # 查找并点击特定机构
            org_selector = f"li[itemvalue='{org_id}'] a"
            try:
                org_option = self.driver.find_element(By.CSS_SELECTOR, org_selector)
                if org_option:
                    org_option.click()
                    time.sleep(3)
                    self.logger.info(f"已选择机构: {org_name}")

                    # 等待页面刷新
                    self.wait.until(EC.presence_of_element_located((By.ID, "policylist")))
                    time.sleep(2)
                    return True
                else:
                    self.logger.warning(f"未找到机构选项: {org_name}")
                    return False
            except Exception as e:
                self.logger.warning(f"点击机构选项失败: {e}")
                return False

        except Exception as e:
            self.logger.warning(f"设置机构筛选失败: {e}")
            return False
    
    def get_policy_list(self) -> List[Dict[str, str]]:
        """获取政策列表（支持分页）"""
        all_policies = []
        current_page = 1
        max_pages = CRAWLER_CONFIG.get("max_pages", 10)  # 限制最大页数

        try:
            self.logger.info("开始获取政策列表")

            while current_page <= max_pages:
                self.logger.info(f"正在处理第 {current_page} 页")

                # 等待页面加载
                time.sleep(3)
                self.wait.until(EC.presence_of_element_located((By.ID, "policylist")))

                # 获取当前页的政策
                page_policies = self._extract_policies_from_current_page()

                if not page_policies:
                    self.logger.info("当前页没有找到政策，停止翻页")
                    break

                all_policies.extend(page_policies)
                self.logger.info(f"第 {current_page} 页找到 {len(page_policies)} 条政策")

                # 尝试翻到下一页
                if not self._go_to_next_page():
                    self.logger.info("没有更多页面，停止翻页")
                    break

                current_page += 1

            self.logger.info(f"总共找到 {len(all_policies)} 条符合条件的政策")
            return all_policies

        except Exception as e:
            self.logger.error(f"获取政策列表失败: {e}")
            return all_policies  # 返回已获取的政策

    def _extract_policies_from_current_page(self) -> List[Dict[str, str]]:
        """从当前页面提取政策"""
        policies = []

        try:
            # 获取页面源码
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')

            # 根据实际页面结构查找政策列表
            policy_items = soup.select("ul#policylist li.market-list")

            for item in policy_items:
                try:
                    # 提取政策标题
                    title_element = item.select_one("p.market-text")
                    if not title_element:
                        continue

                    title = clean_text(title_element.get_text())

                    # 从onclick事件中提取链接
                    onclick = title_element.get('onclick', '')
                    import re

                    full_link = ""
                    # 支持两种链接格式
                    if 'policy_artical.html?policyguid=' in onclick:
                        guid_match = re.search(r'policyguid=([A-F0-9-]+)', onclick)
                        if guid_match:
                            guid = guid_match.group(1)
                            full_link = f"https://nqt.nanjing.gov.cn/nqtmh/njyst/pages/policy_artical.html?policyguid={guid}"
                    elif 'policy_articalsqt.html?policyguid=' in onclick:
                        guid_match = re.search(r'policyguid=([A-F0-9-]+)', onclick)
                        if guid_match:
                            guid = guid_match.group(1)
                            full_link = f"https://nqt.nanjing.gov.cn/nqtmh/njyst/pages/policy_articalsqt.html?policyguid={guid}"

                    if not full_link:
                        continue

                    # 提取发布机构
                    org_element = item.select_one("span.contruct-name")
                    publish_org = clean_text(org_element.get_text()) if org_element else ""

                    # 提取发布日期
                    date_text = item.get_text()
                    date_match = re.search(r'发布日期：(\d{4}-\d{2}-\d{2})', date_text)
                    publish_date = date_match.group(1) if date_match else ""

                    # 检查是否符合筛选条件
                    if self._should_include_policy(publish_org, publish_date):
                        policies.append({
                            'title': title,
                            'link': full_link,
                            'publish_org': publish_org,
                            'publish_date': publish_date
                        })

                except Exception as e:
                    self.logger.warning(f"解析政策项失败: {e}")
                    continue

            return policies

        except Exception as e:
            self.logger.error(f"提取当前页政策失败: {e}")
            return []

    def _go_to_next_page(self) -> bool:
        """翻到下一页"""
        try:
            # 查找"下一页"按钮
            next_button = self.driver.find_element(By.XPATH, "//a[contains(text(), '下一页')]")
            if next_button and next_button.is_enabled():
                next_button.click()
                time.sleep(3)  # 等待页面加载
                return True
            else:
                return False
        except Exception as e:
            self.logger.debug(f"翻页失败: {e}")
            return False

    def _should_include_policy(self, publish_org: str, publish_date: str) -> bool:
        """判断政策是否符合筛选条件 - 由于已经通过页面筛选，这里只做基本验证"""
        try:
            # 由于我们已经通过页面筛选了机构和日期，这里只做基本的数据验证

            # 检查发布日期格式
            if publish_date:
                from datetime import datetime
                try:
                    policy_date = datetime.strptime(publish_date, '%Y-%m-%d')
                    # 基本的日期范围检查（前三年到现在）
                    start_date = datetime.now().replace(year=datetime.now().year-3, month=1, day=1)
                    end_date = datetime.now()

                    if not (start_date <= policy_date <= end_date):
                        return False  # 日期不在范围内
                except ValueError:
                    # 日期格式错误，但不排除该政策
                    pass

            return True

        except Exception as e:
            self.logger.warning(f"筛选条件检查失败: {e}")
            return True  # 出错时默认包含
    
    def extract_policy_detail(self, policy_url: str) -> Optional[Dict[str, Any]]:
        """提取政策详情"""
        try:
            self.logger.info(f"正在提取政策详情: {policy_url}")
            
            # 访问政策详情页
            self.driver.get(policy_url)
            
            # 等待页面加载
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            time.sleep(2)
            
            # 获取页面源码
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # 提取政策信息
            policy_data = {
                'title': self._extract_title(soup),
                'address': policy_url,
                'content': self._extract_content(soup),
                'fbjg': self._extract_publish_org(soup),
                'link': self._extract_attachments(soup, policy_url)
            }
            
            # 验证数据完整性
            if validate_policy_data(policy_data):
                self.logger.info(f"成功提取政策: {policy_data['title']}")
                return policy_data
            else:
                self.logger.warning(f"政策数据不完整: {policy_url}")
                return None
                
        except Exception as e:
            self.logger.error(f"提取政策详情失败 {policy_url}: {e}")
            return None
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """提取标题"""
        # 根据页面分析，标题在#content的第一个p标签中
        content_element = soup.select_one('#content')
        if content_element:
            # 查找第一个包含标题的p标签
            p_tags = content_element.find_all('p')
            for p in p_tags[:3]:  # 检查前3个p标签
                text = clean_text(p.get_text())
                if text and len(text) > 10 and ('通知' in text or '公告' in text or '办法' in text or '规定' in text):
                    return text

        # 备选方案：从页面中查找包含关键词的文本
        page_text = soup.get_text()
        import re

        # 查找标题模式
        title_patterns = [
            r'([^。\n]*(?:通知|公告|办法|规定|意见|决定|细则)[^。\n]*)',
            r'关于[^。\n]*的(?:通知|公告|办法|规定|意见|决定)',
        ]

        for pattern in title_patterns:
            matches = re.findall(pattern, page_text)
            if matches:
                title = clean_text(matches[0])
                if title and len(title) > 10:
                    return title

        return ""

    def _extract_content(self, soup: BeautifulSoup) -> str:
        """提取正文内容"""
        # 根据页面分析，内容在#content或.detail-text中
        content_selectors = [
            '#content',
            '.detail-text',
            '.policy-content',
            '.article-content',
            '.content',
            '[class*="content"]'
        ]

        for selector in content_selectors:
            content_element = soup.select_one(selector)
            if content_element:
                content = extract_text_from_html(str(content_element))
                if content and len(content) > 50:  # 确保内容有意义
                    return content

        # 如果没有找到特定的内容区域，尝试从主要区域提取
        main_content = soup.find('main') or soup.find('article')
        if main_content:
            return extract_text_from_html(str(main_content))

        return ""

    def _extract_publish_org(self, soup: BeautifulSoup) -> str:
        """提取发布机构"""
        # 根据页面分析，发布机构信息在页面文本中
        page_text = soup.get_text()

        # 查找包含"发布部门"的文本，根据页面分析结果
        import re
        org_patterns = [
            r'发布部门[：:]\s*([^\n|]+?)(?:\n|发布日期)',
            r'发布机构[：:]\s*([^\n|]+?)(?:\n|发布日期)',
            r'来源[：:]\s*([^\n|]+?)(?:\n|发布日期)',
        ]

        for pattern in org_patterns:
            match = re.search(pattern, page_text)
            if match:
                org = clean_text(match.group(1))
                if org:
                    return org

        # 从页面内容中提取发布机构（通常在文档末尾）
        content_element = soup.select_one('#content')
        if content_element:
            content_text = content_element.get_text()

            # 查找常见的发布机构模式
            org_patterns = [
                r'([^。\n]*(?:省|市|县|区).*?(?:厅|局|委|办|部)[^。\n]*)',
                r'(江苏省[^。\n]*)',
                r'(南京市[^。\n]*)',
            ]

            for pattern in org_patterns:
                matches = re.findall(pattern, content_text)
                if matches:
                    for match in matches:
                        org = clean_text(match)
                        if org and len(org) > 3 and len(org) < 50:
                            return org

        # 检查是否包含目标机构名称
        for org in FILTER_CONFIG['publish_orgs']:
            if org in page_text:
                return org

        return ""

    def _extract_attachments(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """提取附件链接"""
        attachments = []

        # 根据页面分析，附件链接在特定的模板中
        attachment_selectors = [
            'a.file',  # 根据模板分析得出的选择器
            'a[href$=".pdf"]',
            'a[href$=".doc"]',
            'a[href$=".docx"]',
            'a[href$=".xls"]',
            'a[href$=".xlsx"]',
            'a[href*="attach"]',
            '.file-cont a',
            '[class*="attachment"] a',
            '[class*="download"] a'
        ]

        for selector in attachment_selectors:
            links = soup.select(selector)
            for link in links:
                href = link.get('href')
                if href and href != 'javascript:;':
                    full_url = normalize_url(base_url, href)
                    if full_url not in attachments and is_valid_url(full_url):
                        attachments.append(full_url)

        return attachments

    def crawl_policies_by_org(self) -> List[Dict[str, Any]]:
        """按机构分别爬取政策数据"""
        all_policies = []
        target_orgs = ["市发改委", "市科技局", "市工信局"]

        try:
            self.logger.info("开始按机构分别爬取政策数据")

            # 设置WebDriver
            self.setup_driver()

            for org in target_orgs:
                self.logger.info(f"开始爬取 {org} 的政策")

                # 访问目标页面
                if not self.navigate_to_target_page():
                    continue

                # 设置基础筛选条件（发布层级：市 + 日期范围）
                self._set_org_filter()

                # 设置特定机构筛选
                if not self.set_specific_org_filter(org):
                    self.logger.warning(f"无法设置 {org} 筛选，跳过")
                    continue

                # 获取该机构的政策列表
                org_policies = self.get_policy_list()

                if org_policies:
                    self.logger.info(f"{org} 找到 {len(org_policies)} 条政策")

                    # 逐个提取政策详情
                    for i, policy_info in enumerate(org_policies, 1):
                        try:
                            self.logger.info(f"处理 {org} 第 {i}/{len(org_policies)} 条政策")

                            # 提取详情
                            policy_detail = self.extract_policy_detail(policy_info['link'])

                            if policy_detail:
                                # 确保发布机构信息正确
                                policy_detail['fbjg'] = f"南京市-{org}"
                                all_policies.append(policy_detail)

                            # 添加延时避免请求过快
                            wait_random_time(
                                CRAWLER_CONFIG["request_delay"],
                                CRAWLER_CONFIG["request_delay"] + 1
                            )

                        except Exception as e:
                            self.logger.error(f"处理 {org} 政策失败: {e}")
                            continue
                else:
                    self.logger.warning(f"{org} 未找到任何政策")

            self.logger.info(f"所有机构爬取完成，共获取 {len(all_policies)} 条有效政策")
            return all_policies

        except Exception as e:
            self.logger.error(f"爬取过程出错: {e}")
            return all_policies

        finally:
            self.close_driver()

    def crawl_policies(self) -> List[Dict[str, Any]]:
        """爬取所有政策数据 - 兼容原有接口"""
        return self.crawl_policies_by_org()

    def save_data(self, data: List[Dict[str, Any]]) -> None:
        """保存数据到文件"""
        if not data:
            self.logger.warning("没有数据需要保存")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        try:
            # 保存为JSON格式
            if "json" in OUTPUT_CONFIG["output_formats"]:
                json_file = os.path.join(
                    OUTPUT_CONFIG["data_dir"],
                    f"{OUTPUT_CONFIG['filename_prefix']}_{timestamp}.json"
                )
                save_data_to_json(data, json_file)
                self.logger.info(f"数据已保存到JSON文件: {json_file}")

            # 保存为CSV格式
            if "csv" in OUTPUT_CONFIG["output_formats"]:
                csv_file = os.path.join(
                    OUTPUT_CONFIG["data_dir"],
                    f"{OUTPUT_CONFIG['filename_prefix']}_{timestamp}.csv"
                )
                save_data_to_csv(data, csv_file)
                self.logger.info(f"数据已保存到CSV文件: {csv_file}")

        except Exception as e:
            self.logger.error(f"保存数据失败: {e}")

    def run(self) -> None:
        """运行爬虫"""
        self.logger.info("=" * 50)
        self.logger.info("南京市政策爬虫开始运行")
        self.logger.info("=" * 50)

        start_time = datetime.now()

        try:
            # 爬取数据
            policies = self.crawl_policies()

            # 保存数据
            if policies:
                self.save_data(policies)

                # 输出统计信息
                self.logger.info(f"爬取统计:")
                self.logger.info(f"- 总计政策数量: {len(policies)}")

                # 按发布机构统计
                org_stats = {}
                for policy in policies:
                    org = policy.get('fbjg', '未知')
                    org_stats[org] = org_stats.get(org, 0) + 1

                for org, count in org_stats.items():
                    self.logger.info(f"- {org}: {count} 条")

            else:
                self.logger.warning("未获取到任何政策数据")

        except Exception as e:
            self.logger.error(f"爬虫运行出错: {e}")

        finally:
            end_time = datetime.now()
            duration = end_time - start_time
            self.logger.info(f"爬虫运行完成，耗时: {duration}")
            self.logger.info("=" * 50)
