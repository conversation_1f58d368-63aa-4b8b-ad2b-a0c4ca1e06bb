#!/usr/bin/env python3
"""
简化测试脚本 - 测试基本的数据提取功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from crawler import NanjingPolicyCrawler


def simple_test():
    """简化测试"""
    print("开始简化测试...")
    
    crawler = NanjingPolicyCrawler()
    
    try:
        # 设置WebDriver
        crawler.setup_driver()
        print("✓ WebDriver初始化成功")
        
        # 访问目标页面
        if crawler.navigate_to_target_page():
            print("✓ 成功访问目标页面")
        else:
            print("✗ 访问目标页面失败")
            return False
        
        # 获取政策列表（不进行筛选，获取所有政策）
        import time
        time.sleep(5)
        
        # 等待政策列表加载
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        from selenium.webdriver.common.by import By
        
        wait = WebDriverWait(crawler.driver, 20)
        wait.until(EC.presence_of_element_located((By.ID, "policylist")))
        
        # 获取页面源码
        from bs4 import BeautifulSoup
        page_source = crawler.driver.page_source
        soup = BeautifulSoup(page_source, 'html.parser')
        
        # 查找政策列表
        policy_items = soup.select("ul#policylist li.market-list")
        print(f"✓ 找到 {len(policy_items)} 个政策列表项")
        
        if policy_items:
            # 测试提取第一条政策的基本信息
            first_item = policy_items[0]
            
            # 提取标题
            title_element = first_item.select_one("p.market-text")
            if title_element:
                title = title_element.get_text().strip()
                print(f"✓ 提取标题: {title[:50]}...")
                
                # 提取链接
                onclick = title_element.get('onclick', '')
                if 'policy_articalsqt.html?policyguid=' in onclick:
                    import re
                    guid_match = re.search(r'policyguid=([A-F0-9-]+)', onclick)
                    if guid_match:
                        guid = guid_match.group(1)
                        full_link = f"https://nqt.nanjing.gov.cn/nqtmh/njyst/pages/policy_articalsqt.html?policyguid={guid}"
                        print(f"✓ 提取链接: {full_link}")
                        
                        # 提取发布机构
                        org_element = first_item.select_one("span.contruct-name")
                        if org_element:
                            publish_org = org_element.get_text().strip()
                            print(f"✓ 提取发布机构: {publish_org}")
                        
                        # 提取发布日期
                        date_text = first_item.get_text()
                        date_match = re.search(r'发布日期：(\d{4}-\d{2}-\d{2})', date_text)
                        if date_match:
                            publish_date = date_match.group(1)
                            print(f"✓ 提取发布日期: {publish_date}")
                        
                        # 测试详情页面提取
                        print("\n测试详情页面提取...")
                        policy_detail = crawler.extract_policy_detail(full_link)
                        
                        if policy_detail:
                            print("✓ 成功提取政策详情")
                            print(f"详情标题: {policy_detail['title'][:50]}...")
                            print(f"发布机构: {policy_detail['fbjg']}")
                            print(f"内容长度: {len(policy_detail['content'])} 字符")
                            print(f"附件数量: {len(policy_detail['link'])} 个")
                            
                            # 保存测试数据
                            test_data = [policy_detail]
                            crawler.save_data(test_data)
                            print("✓ 测试数据已保存")
                            
                            return True
                        else:
                            print("✗ 提取政策详情失败")
                            return False
        
        return False
        
    except Exception as e:
        print(f"✗ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        crawler.close_driver()
        print("✓ WebDriver已关闭")


def main():
    """主函数"""
    print("=" * 60)
    print("南京市政策爬虫简化测试")
    print("=" * 60)
    
    if simple_test():
        print("\n✓ 简化测试通过！基本功能正常")
    else:
        print("\n✗ 简化测试失败")


if __name__ == "__main__":
    main()
