#!/usr/bin/env python3
"""
分析详情页面结构
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup


def analyze_detail_page():
    """分析详情页面"""
    # 测试URL
    detail_url = "https://nqt.nanjing.gov.cn/nqtmh/njyst/pages/policy_articalsqt.html?policyguid=5C2C4430-D2A2-4C19-895E-A4F554D070C8"
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    driver = webdriver.Chrome(options=chrome_options)
    
    try:
        print(f"正在访问详情页面: {detail_url}")
        driver.get(detail_url)
        
        # 等待页面加载
        time.sleep(10)
        
        print(f"页面标题: {driver.title}")
        print(f"当前URL: {driver.current_url}")
        
        # 保存页面源码
        page_source = driver.page_source
        with open("data/detail_page_analysis.html", "w", encoding="utf-8") as f:
            f.write(page_source)
        
        # 解析页面
        soup = BeautifulSoup(page_source, 'html.parser')
        
        print("\n=== 页面结构分析 ===")
        
        # 查找标题
        print("\n1. 查找标题:")
        title_selectors = ['h1', 'h2', '.title', '.policy-title', '.article-title']
        for selector in title_selectors:
            elements = soup.select(selector)
            if elements:
                for i, elem in enumerate(elements[:3]):
                    text = elem.get_text().strip()[:100]
                    print(f"  {selector}[{i}]: {text}")
        
        # 查找内容区域
        print("\n2. 查找内容区域:")
        content_selectors = ['#content', '.detail-text', '.policy-content', '.article-content', '.content']
        for selector in content_selectors:
            elements = soup.select(selector)
            if elements:
                for i, elem in enumerate(elements[:2]):
                    text = elem.get_text().strip()[:200]
                    print(f"  {selector}[{i}]: {text}...")
        
        # 查找发布信息
        print("\n3. 查找发布信息:")
        # 查找包含"发布"的文本
        import re
        page_text = soup.get_text()
        
        # 查找发布部门
        org_patterns = [
            r'发布部门[：:]\s*([^|]+)',
            r'发布机构[：:]\s*([^|]+)',
            r'来源[：:]\s*([^|]+)',
        ]
        
        for pattern in org_patterns:
            matches = re.findall(pattern, page_text)
            if matches:
                print(f"  {pattern}: {matches}")
        
        # 查找发布日期
        date_patterns = [
            r'发布日期[：:]\s*(\d{4}-\d{2}-\d{2})',
            r'发布时间[：:]\s*(\d{4}-\d{2}-\d{2})',
        ]
        
        for pattern in date_patterns:
            matches = re.findall(pattern, page_text)
            if matches:
                print(f"  {pattern}: {matches}")
        
        # 查找附件
        print("\n4. 查找附件:")
        attachment_selectors = [
            'a.file',
            'a[href$=".pdf"]',
            'a[href$=".doc"]',
            'a[href$=".docx"]',
            'a[href*="attach"]',
            '.file-cont a',
            '[class*="attachment"] a'
        ]
        
        for selector in attachment_selectors:
            elements = soup.select(selector)
            if elements:
                print(f"  {selector}: 找到 {len(elements)} 个")
                for elem in elements[:3]:
                    href = elem.get('href', '')
                    text = elem.get_text().strip()
                    print(f"    {text} -> {href}")
        
        # 查找所有可能的内容容器
        print("\n5. 查找所有div容器:")
        divs = soup.find_all('div', class_=True)
        div_classes = {}
        for div in divs:
            classes = ' '.join(div.get('class', []))
            if classes:
                div_classes[classes] = div_classes.get(classes, 0) + 1
        
        # 显示最常见的div类
        sorted_classes = sorted(div_classes.items(), key=lambda x: x[1], reverse=True)
        print("  最常见的div类:")
        for class_name, count in sorted_classes[:10]:
            print(f"    {class_name}: {count} 个")
        
        # 检查是否有JavaScript动态加载的内容
        print("\n6. 检查动态内容:")
        scripts = soup.find_all('script')
        print(f"  找到 {len(scripts)} 个script标签")
        
        # 查找可能的AJAX请求
        for script in scripts:
            script_text = script.get_text()
            if 'ajax' in script_text.lower() or 'fetch' in script_text.lower():
                print("  发现可能的AJAX请求")
                break
        
        print(f"\n详情页面分析完成！")
        print(f"页面源码已保存到: data/detail_page_analysis.html")
        
    except Exception as e:
        print(f"分析过程出错: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        driver.quit()


if __name__ == "__main__":
    print("=" * 60)
    print("详情页面结构分析工具")
    print("=" * 60)
    
    # 创建数据目录
    os.makedirs("data", exist_ok=True)
    
    analyze_detail_page()
