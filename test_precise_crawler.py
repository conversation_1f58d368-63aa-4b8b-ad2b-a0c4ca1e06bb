#!/usr/bin/env python3
"""
测试精确筛选逻辑的爬虫
按照人工筛选逻辑：
1. 发布层级选择：市
2. 发布机构分别选择：市发改委、市科技局、市工信局
3. 发布日期：前三年1月1日到现在
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from crawler import NanjingPolicyCrawler


def test_precise_filtering():
    """测试精确筛选逻辑"""
    print("=" * 60)
    print("测试精确筛选逻辑")
    print("=" * 60)
    
    crawler = NanjingPolicyCrawler()
    
    try:
        # 设置WebDriver
        crawler.setup_driver()
        print("✓ WebDriver初始化成功")
        
        # 测试每个机构的筛选
        target_orgs = ["市发改委", "市科技局", "市工信局"]
        org_results = {}
        
        for org in target_orgs:
            print(f"\n--- 测试 {org} 筛选 ---")
            
            # 访问目标页面
            if crawler.navigate_to_target_page():
                print(f"✓ 成功访问目标页面")
                
                # 设置基础筛选条件
                crawler._set_org_filter()
                print(f"✓ 已设置发布层级为'市'和日期范围")
                
                # 设置特定机构筛选
                if crawler.set_specific_org_filter(org):
                    print(f"✓ 已设置机构筛选: {org}")
                    
                    # 获取政策列表
                    policies = crawler._extract_policies_from_current_page()
                    org_results[org] = len(policies)
                    
                    print(f"✓ {org} 找到 {len(policies)} 条政策")
                    
                    # 显示前几条政策标题
                    if policies:
                        print(f"  前3条政策:")
                        for i, policy in enumerate(policies[:3], 1):
                            title = policy['title'][:50] + "..." if len(policy['title']) > 50 else policy['title']
                            print(f"    {i}. {title}")
                            print(f"       发布机构: {policy.get('publish_org', '未知')}")
                            print(f"       发布日期: {policy.get('publish_date', '未知')}")
                    
                else:
                    print(f"✗ 无法设置 {org} 筛选")
                    org_results[org] = 0
            else:
                print(f"✗ 访问目标页面失败")
                org_results[org] = 0
        
        # 输出统计结果
        print(f"\n" + "=" * 60)
        print("筛选结果统计:")
        print("=" * 60)
        
        total_policies = 0
        for org, count in org_results.items():
            print(f"{org}: {count} 条政策")
            total_policies += count
        
        print(f"总计: {total_policies} 条政策")
        
        # 与预期结果对比
        expected_results = {
            "市发改委": 8,
            "市科技局": 19,
            "市工信局": 5
        }
        
        print(f"\n预期结果对比:")
        for org in target_orgs:
            actual = org_results.get(org, 0)
            expected = expected_results.get(org, 0)
            status = "✓" if actual == expected else "✗"
            print(f"{status} {org}: 实际 {actual} 条, 预期 {expected} 条")
        
        return org_results
        
    except Exception as e:
        print(f"✗ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return {}
        
    finally:
        crawler.close_driver()
        print("✓ WebDriver已关闭")


def test_full_crawl_by_org():
    """测试完整的按机构爬取"""
    print(f"\n" + "=" * 60)
    print("测试完整的按机构爬取（包含详情提取）")
    print("=" * 60)
    
    crawler = NanjingPolicyCrawler()
    
    try:
        # 限制爬取数量进行测试
        from crawler.config import CRAWLER_CONFIG
        original_max_pages = CRAWLER_CONFIG.get("max_pages", 50)
        CRAWLER_CONFIG["max_pages"] = 1  # 只爬取第一页进行测试
        
        # 运行按机构爬取
        policies = crawler.crawl_policies_by_org()
        
        # 恢复原始设置
        CRAWLER_CONFIG["max_pages"] = original_max_pages
        
        print(f"✓ 完整爬取完成，共获取 {len(policies)} 条政策")
        
        # 按机构统计
        org_stats = {}
        for policy in policies:
            org = policy.get('fbjg', '未知')
            org_stats[org] = org_stats.get(org, 0) + 1
        
        print(f"\n按机构统计:")
        for org, count in org_stats.items():
            print(f"  {org}: {count} 条")
        
        # 保存测试数据
        if policies:
            crawler.save_data(policies)
            print(f"✓ 测试数据已保存")
        
        return len(policies)
        
    except Exception as e:
        print(f"✗ 完整爬取测试出错: {e}")
        import traceback
        traceback.print_exc()
        return 0


def main():
    """主函数"""
    print("南京市政策爬虫 - 精确筛选测试")
    
    # 测试筛选逻辑
    org_results = test_precise_filtering()
    
    # 如果筛选测试成功，进行完整爬取测试
    if org_results and any(count > 0 for count in org_results.values()):
        test_full_crawl_by_org()
    else:
        print("\n筛选测试失败，跳过完整爬取测试")


if __name__ == "__main__":
    main()
