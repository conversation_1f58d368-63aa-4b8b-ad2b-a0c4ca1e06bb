"""
南京市政策爬虫配置文件
"""
from datetime import datetime, timedelta

# 目标网站配置
TARGET_URL = "https://nqt.nanjing.gov.cn/nqtmh/njyst/pages/policy_market.html"

# 筛选条件配置
FILTER_CONFIG = {
    # 发布层级：市
    "publish_level": "市",
    
    # 发布机构
    "publish_orgs": ["市发改委", "市科技局", "市工信局"],
    
    # 发布时间：从三年前的1月1日到今天
    "start_date": "2022-01-01",
    "end_date": datetime.now().strftime("%Y-%m-%d")
}

# Selenium配置
SELENIUM_CONFIG = {
    "implicit_wait": 10,
    "page_load_timeout": 30,
    "script_timeout": 30,
    "window_size": (1920, 1080)
}

# Chrome浏览器配置
CHROME_OPTIONS = [
    "--no-sandbox",
    "--disable-dev-shm-usage",
    "--disable-gpu",
    "--disable-extensions",
    "--disable-plugins",
    "--disable-images",  # 禁用图片加载以提高速度
    "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
]

# 爬虫行为配置
CRAWLER_CONFIG = {
    "request_delay": 2,  # 请求间隔（秒）
    "max_retries": 3,    # 最大重试次数
    "timeout": 30,       # 超时时间（秒）
    "max_pages": 50      # 最大爬取页数（防止无限循环）
}

# 数据提取配置
EXTRACTION_CONFIG = {
    # CSS选择器配置
    "selectors": {
        # 主页面选择器
        "policy_list": "ul#policylist li.market-list",  # 政策列表项选择器
        "policy_title": "p.market-text",  # 政策标题选择器
        "policy_onclick": "p.market-text",  # 包含onclick事件的元素
        "publish_org": "span.contruct-name",  # 发布机构选择器
        "publish_date": ".market-info",  # 发布日期容器选择器

        # 详情页面选择器
        "detail_title": "h1, h2, .title, .policy-title",  # 详情页标题选择器
        "detail_content": "#content, .detail-text, .policy-content, .article-content",  # 政策内容选择器
        "detail_publish_org": ".publish-org, .contruct-name",  # 详情页发布机构选择器
        "attachments": "a.file, a[href$='.pdf'], a[href$='.doc'], a[href$='.docx'], a[href$='.xls'], a[href$='.xlsx']",  # 附件链接选择器

        # 筛选器选择器
        "level_filter": "ul#fbcj li",  # 发布层级筛选
        "org_filter": ".item-list",  # 机构筛选
        "date_filter": ".date-picker"  # 日期筛选
    }
}

# 输出配置
OUTPUT_CONFIG = {
    "data_dir": "data",
    "log_dir": "logs",
    "output_formats": ["json", "csv"],
    "filename_prefix": "nanjing_policies",
    "encoding": "utf-8"
}

# 日志配置
LOG_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file_handler": True,
    "console_handler": True
}
